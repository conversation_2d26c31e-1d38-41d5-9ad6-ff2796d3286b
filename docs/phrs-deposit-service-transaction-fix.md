# PHRS充值服务事务处理修复

## 问题描述

在PHRS充值服务中出现了事务回滚错误：

```
未注册用户记录创建失败: Error: rollback has been called on this transaction(2c1f21d0-ec0d-42a0-b85a-43a4f4421cbd), you can no longer use it. (The rejected query is attached as the 'sql' property of this error)
```

## 根本原因分析

1. **事务重复回滚**：代码中存在多次回滚同一个事务的情况
2. **并发处理问题**：多个请求同时处理相同的交易哈希，导致唯一约束冲突
3. **事务状态管理不当**：没有正确跟踪事务的提交/回滚状态
4. **错误处理不完善**：在catch块中没有正确处理已经完成的事务

## 修复方案

### 1. 添加安全回滚方法

```typescript
/**
 * 修复：安全地回滚事务
 */
private async safeRollback(transaction: any, context: string): Promise<void> {
  if (!transaction) {
    return;
  }

  try {
    await transaction.rollback();
  } catch (rollbackError: any) {
    // 忽略已经完成的事务错误
    if (!rollbackError.message.includes('finished') && 
        !rollbackError.message.includes('rollback') &&
        !rollbackError.message.includes('already been committed or rolled back')) {
      console.warn(`${context} 事务回滚失败:`, rollbackError);
    }
  }
}
```

### 2. 改进事务状态跟踪

在所有事务处理方法中添加 `transactionCommitted` 标志：

```typescript
let transaction;
let transactionCommitted = false;

try {
  transaction = await sequelize.transaction();
  
  // 业务逻辑...
  
  await transaction.commit();
  transactionCommitted = true;
  
} catch (error) {
  if (transaction && !transactionCommitted) {
    await this.safeRollback(transaction, '上下文描述');
    transactionCommitted = true;
  }
}
```

### 3. 增强重复检查机制

在事件处理前添加预检查：

```typescript
// 修复：在处理前先检查是否已存在记录，避免不必要的处理
const existingRecord = await PhrsDeposit.findOne({
  where: { transactionHash: eventLog.transactionHash }
});

if (existingRecord) {
  console.log(`⚠️  事件已处理过，跳过: ${eventLog.transactionHash}`);
  successCount++; // 算作成功，因为记录已存在
  processedEvents.push(eventLog.transactionHash);
  continue;
}
```

### 4. 改进并发冲突处理

对唯一约束冲突进行特殊处理：

```typescript
} catch (error) {
  // 修复：如果是唯一约束冲突，不算作失败
  if (error instanceof Error && 
      (error.name === 'SequelizeUniqueConstraintError' || 
       error.message.includes('Duplicate entry') ||
       error.message.includes('UNIQUE constraint failed'))) {
    console.log(`⚠️  事件重复处理（并发冲突），算作成功: ${event.transactionHash}`);
    successCount++;
    processedEvents.push(event.transactionHash);
  } else {
    failureCount++;
    failedEvents.push({
      txHash: event.transactionHash,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}
```

## 修复的方法

### 1. `handleUnregisteredUserDeposit`
- 添加事务状态跟踪
- 使用安全回滚方法
- 改进并发检查逻辑

### 2. `recordFailedEvent`
- 添加预检查避免重复处理
- 使用安全回滚方法
- 改进错误处理

### 3. `handleDepositEvent`
- 添加事务状态跟踪
- 使用安全回滚方法
- 改进主事务处理逻辑

### 4. `processEventsWithTracking`
- 添加预检查机制
- 改进并发冲突处理
- 将唯一约束冲突视为成功

## 测试验证

创建了测试脚本 `src/scripts/testPhrsDepositService.ts` 来验证修复效果：

```bash
# 运行测试
npx ts-node src/scripts/testPhrsDepositService.ts
```

测试内容包括：
1. 数据库连接检查
2. 服务状态检查
3. 健康检查
4. 现有记录检查
5. 区块处理测试
6. 并发处理测试

## 预期效果

1. **消除事务回滚错误**：不再出现重复回滚事务的错误
2. **提高并发处理能力**：正确处理并发请求导致的唯一约束冲突
3. **增强系统稳定性**：更好的错误处理和恢复机制
4. **减少重复处理**：通过预检查避免不必要的事务操作

## 注意事项

1. 修复后的代码更加保守，优先保证数据一致性
2. 唯一约束冲突被视为正常情况，不会导致处理失败
3. 所有事务操作都有完善的状态跟踪和错误处理
4. 建议在生产环境部署前进行充分测试

## 相关文件

- `src/services/phrsDepositService.ts` - 主要修复文件
- `src/scripts/testPhrsDepositService.ts` - 测试脚本
- `src/models/PhrsDeposit.ts` - 数据模型（包含唯一约束）
- `migrations/20250718000000-fix-phrs-deposits-precision-and-constraints.js` - 数据库迁移
