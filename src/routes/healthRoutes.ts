// src/routes/healthRoutes.ts
import { Router, Request, Response } from 'express';
import { sequelize } from '../config/db';
import { redis } from '../config/redis';

const router = Router();

// 健康检查端点
router.get('/health', async (req: Request, res: Response) => {
  const startTime = Date.now();
  
  try {
    // 检查数据库连接
    await sequelize.authenticate();
    const dbStatus = 'ok';
    
    // 检查Redis连接
    await redis.ping();
    const redisStatus = 'ok';
    
    // 检查响应时间
    const responseTime = Date.now() - startTime;
    
    res.status(200).json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      services: {
        database: dbStatus,
        redis: redisStatus
      },
      responseTime: `${responseTime}ms`,
      uptime: process.uptime(),
      memory: {
        used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
        total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024),
        rss: Math.round(process.memoryUsage().rss / 1024 / 1024)
      }
    });
  } catch (error) {
    console.error('健康检查失败:', error);
    res.status(503).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
});

// 简单的存活检查
router.get('/ping', (req: Request, res: Response) => {
  res.status(200).json({ 
    status: 'ok', 
    timestamp: new Date().toISOString() 
  });
});

export default router;
