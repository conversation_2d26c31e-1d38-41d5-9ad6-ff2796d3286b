// src/services/accumulatedOfflineRewardService.ts

import { UserWallet } from '../models/UserWallet';
import { DeliveryLine } from '../models/DeliveryLine';
import { FarmPlot } from '../models/FarmPlot';
import { sequelize } from '../config/db';
import { Transaction } from 'sequelize';
import { createBigNumber, formatToThreeDecimalsNumber } from '../utils/bigNumberConfig';

/**
 * 累积离线奖励服务
 * 实现离线奖励累积机制，如果用户不领取奖励，奖励会持续累积
 */
export class AccumulatedOfflineRewardService {

  /**
   * 计算并更新累积的离线奖励
   * @param walletId 钱包ID
   * @param transaction 数据库事务
   * @returns 累积的奖励信息
   */
  static async calculateAndUpdateAccumulatedRewards(
    walletId: number, 
    transaction?: Transaction
  ): Promise<{
    totalAccumulatedGems: number;
    newlyAccumulatedGems: number;
    lastCalculationTime: Date | null;
    currentTime: Date;
  }> {
    const useTransaction = transaction || await sequelize.transaction();
    const shouldCommit = !transaction;

    try {
      // 获取用户钱包信息
      const wallet = await UserWallet.findOne({
        where: { id: walletId },
        transaction: useTransaction
      });

      if (!wallet) {
        throw new Error('用户钱包不存在');
      }

      const now = new Date();
      
      // 如果用户从未活跃过，不计算离线奖励
      if (!wallet.lastActiveTime) {
        if (shouldCommit) await useTransaction.commit();
        return {
          totalAccumulatedGems: 0,
          newlyAccumulatedGems: 0,
          lastCalculationTime: null,
          currentTime: now
        };
      }

      // 确定上次计算时间
      // 如果从未计算过，使用lastActiveTime作为起始时间
      const lastCalculationTime = wallet.lastOfflineRewardCalculation || wallet.lastActiveTime;

      // 确保时间是Date对象
      const lastCalculationDate = lastCalculationTime instanceof Date ? lastCalculationTime : new Date(lastCalculationTime);

      // 计算从上次计算到现在的时间差（秒）
      const timeSinceLastCalculation = Math.floor((now.getTime() - lastCalculationDate.getTime()) / 1000);
      
      // 如果时间差小于120秒（2分钟），不计算新的奖励
      if (timeSinceLastCalculation < 120) {
        if (shouldCommit) await useTransaction.commit();
        return {
          totalAccumulatedGems: formatToThreeDecimalsNumber(wallet.accumulatedOfflineGems || 0),
          newlyAccumulatedGems: 0,
          lastCalculationTime: wallet.lastOfflineRewardCalculation || null,
          currentTime: now
        };
      }

      // 计算新的离线奖励
      const newOfflineReward = await this.calculateOfflineRewardForPeriod(
        walletId, 
        timeSinceLastCalculation, 
        useTransaction
      );

      // 更新累积奖励
      const currentAccumulated = createBigNumber(wallet.accumulatedOfflineGems || 0);
      const newAccumulated = currentAccumulated.plus(newOfflineReward);
      
      wallet.accumulatedOfflineGems = formatToThreeDecimalsNumber(newAccumulated);
      wallet.lastOfflineRewardCalculation = now;
      
      await wallet.save({ transaction: useTransaction });

      if (shouldCommit) await useTransaction.commit();

      return {
        totalAccumulatedGems: formatToThreeDecimalsNumber(newAccumulated),
        newlyAccumulatedGems: formatToThreeDecimalsNumber(newOfflineReward),
        lastCalculationTime: lastCalculationDate,
        currentTime: now
      };

    } catch (error) {
      if (shouldCommit) {
        try {
          await useTransaction.rollback();
        } catch (rollbackError) {
          // 事务可能已经提交，忽略回滚错误
        }
      }
      throw error;
    }
  }

  /**
   * 计算指定时间段的离线奖励
   * @param walletId 钱包ID
   * @param timeInSeconds 时间段（秒）
   * @param transaction 数据库事务
   * @returns 奖励数量
   */
  private static async calculateOfflineRewardForPeriod(
    walletId: number,
    timeInSeconds: number,
    transaction: Transaction
  ): Promise<number> {
    // 使用现有的TimeWarpService计算逻辑
    const { TimeWarpService } = require('./timeWarpService');
    const timeInHours = timeInSeconds / 3600;
    
    // 限制最大离线奖励时间为8小时
    const effectiveTimeInHours = Math.min(timeInHours, 8);
    
    const rewardResult = await TimeWarpService.calculateTimeWarpRewards(walletId, effectiveTimeInHours);
    
    return formatToThreeDecimalsNumber(rewardResult.gemsEarned || 0);
  }

  /**
   * 领取累积的离线奖励
   * @param walletId 钱包ID
   * @param transaction 数据库事务
   * @returns 领取的奖励信息
   */
  static async claimAccumulatedRewards(
    walletId: number,
    transaction?: Transaction
  ): Promise<{
    claimedGems: number;
    remainingGems: number;
  }> {
    const useTransaction = transaction || await sequelize.transaction();
    const shouldCommit = !transaction;

    try {
      // 先更新累积奖励
      await this.calculateAndUpdateAccumulatedRewards(walletId, useTransaction);

      // 获取最新的钱包信息
      const wallet = await UserWallet.findOne({
        where: { id: walletId },
        transaction: useTransaction
      });

      if (!wallet) {
        throw new Error('用户钱包不存在');
      }

      const accumulatedGems = formatToThreeDecimalsNumber(wallet.accumulatedOfflineGems || 0);
      
      if (accumulatedGems <= 0) {
        if (shouldCommit) await useTransaction.commit();
        return {
          claimedGems: 0,
          remainingGems: 0
        };
      }

      // 将累积奖励添加到用户的GEM余额
      const currentGemBN = createBigNumber(wallet.gem || 0);
      const accumulatedGemBN = createBigNumber(accumulatedGems);
      const newGemBN = currentGemBN.plus(accumulatedGemBN);
      
      wallet.gem = newGemBN.toFixed(3);
      wallet.accumulatedOfflineGems = 0; // 清空累积奖励
      wallet.lastActiveTime = new Date(); // 更新活跃时间
      
      await wallet.save({ transaction: useTransaction });

      if (shouldCommit) await useTransaction.commit();

      return {
        claimedGems: accumulatedGems,
        remainingGems: 0
      };

    } catch (error) {
      if (shouldCommit) {
        try {
          await useTransaction.rollback();
        } catch (rollbackError) {
          // 事务可能已经提交，忽略回滚错误
        }
      }
      throw error;
    }
  }

  /**
   * 获取累积奖励信息（不领取）
   * @param walletId 钱包ID
   * @returns 累积奖励信息
   */
  static async getAccumulatedRewardInfo(walletId: number): Promise<{
    totalAccumulatedGems: number;
    isOffline: boolean;
    offlineTime: number;
    lastActiveTime: Date | null;
    lastCalculationTime: Date | null;
  }> {
    try {
      // 计算并更新累积奖励（内部会管理自己的事务）
      const rewardInfo = await this.calculateAndUpdateAccumulatedRewards(walletId);

      // 获取用户钱包信息
      const wallet = await UserWallet.findOne({
        where: { id: walletId }
      });

      if (!wallet) {
        throw new Error('用户钱包不存在');
      }

      const now = new Date();
      const lastActiveTime = wallet.lastActiveTime;
      const lastActiveDate = lastActiveTime instanceof Date ? lastActiveTime : (lastActiveTime ? new Date(lastActiveTime) : null);
      const offlineTime = lastActiveDate ?
        Math.floor((now.getTime() - lastActiveDate.getTime()) / 1000) : 0;
      const isOffline = offlineTime >= 120;

      return {
        totalAccumulatedGems: rewardInfo.totalAccumulatedGems,
        isOffline,
        offlineTime,
        lastActiveTime: lastActiveDate,
        lastCalculationTime: rewardInfo.lastCalculationTime
      };

    } catch (error) {
      throw error;
    }
  }
}
