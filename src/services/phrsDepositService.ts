// src/services/phrsDepositService.ts
import { ethers } from 'ethers';
import { UserWallet, PhrsDeposit } from '../models';
import { sequelize } from '../config/db';
import BigNumber from 'bignumber.js';

/**
 * PHRS充值服务
 * 负责监听区块链事件并处理PHRS代币充值
 */
export class PhrsDepositService {
  public provider: ethers.JsonRpcProvider;
  private contract: ethers.Contract;
  private contractAddress: string;
  private isListening: boolean = false;
  private lastProcessedBlock: number = 0;
  private readonly historicalStartBlock: number;
  private pollTimer: NodeJS.Timeout | null = null; // 修复：添加定时器引用防止内存泄漏

  // 合约ABI - 新版本合约的ABI
  private readonly contractABI = [
    "event Deposit(address indexed user, uint256 amount, uint256 timestamp)",
    "function getBalance() external view returns (uint256)",
    "function getContractInfo() external view returns (uint256, uint256, uint256, uint256)",
    "function getUserBalance(address user) external view returns (uint256)",
    "function detectForcedDeposits() external view returns (uint256, bool)"
  ];

  constructor() {
    const rpcUrl = process.env.PHAROS_RPC_URL || 'https://api.zan.top/node/v1/pharos/testnet/d42f5024864e431388c182f9e5052d47';
    this.contractAddress = process.env.PHRS_DEPOSIT_CONTRACT_ADDRESS || '';

    if (!this.contractAddress) {
      throw new Error('PHRS_DEPOSIT_CONTRACT_ADDRESS environment variable is required');
    }

    // 设置历史事件处理的起始区块（可通过环境变量配置）
    this.historicalStartBlock = parseInt(process.env.PHRS_HISTORICAL_START_BLOCK || '0');

    this.provider = new ethers.JsonRpcProvider(rpcUrl);
    this.contract = new ethers.Contract(this.contractAddress, this.contractABI, this.provider);

    console.log(`PHRS充值服务初始化完成 - 合约地址: ${this.contractAddress}`);
    console.log(`历史事件处理起始区块: ${this.historicalStartBlock}`);
  }

  /**
   * 开始监听充值事件
   */
  public async startListening(): Promise<void> {
    if (this.isListening) {
      console.log('PHRS充值监听服务已在运行');
      return;
    }

    try {
      // 获取最后处理的区块号
      await this.loadLastProcessedBlock();

      console.log(`开始监听PHRS充值事件，从区块 ${this.lastProcessedBlock} 开始`);

      // 先设置监听状态为true
      this.isListening = true;

      // 使用轮询模式监听充值事件（避免eth_newFilter问题）
      this.startPolling();

      // 处理历史事件（如果有遗漏的）
      await this.processHistoricalEvents();

      console.log('✅ PHRS充值监听服务启动成功');
      
    } catch (error) {
      console.error('❌ PHRS充值监听服务启动失败:', error);
      throw error;
    }
  }

  /**
   * 停止监听充值事件
   */
  public async stopListening(): Promise<void> {
    if (!this.isListening) {
      return;
    }

    this.isListening = false;
    
    // 修复：清理定时器防止内存泄漏
    if (this.pollTimer) {
      clearTimeout(this.pollTimer);
      this.pollTimer = null;
    }
    
    console.log('PHRS充值监听服务已停止');
  }

  /**
   * 修复：改进轮询机制，避免内存泄漏
   */
  private startPolling(): void {
    const pollInterval = 10000; // 10秒轮询一次

    console.log(`🔄 启动轮询，间隔: ${pollInterval/1000}秒`);

    const poll = async () => {
      // 双重检查确保服务仍在运行
      if (!this.isListening) {
        console.log('⏹️  轮询已停止');
        return;
      }

      try {
        console.log(`🔍 执行轮询检查... (${new Date().toLocaleTimeString()})`);
        await this.checkForNewDeposits();
      } catch (error) {
        console.error('轮询检查充值事件时出错:', error);

        // 如果是网络错误，等待更长时间再重试
        if (error instanceof Error && (
          error.message.includes('network') ||
          error.message.includes('timeout') ||
          error.message.includes('ECONNREFUSED')
        )) {
          console.log('🌐 检测到网络问题，延长轮询间隔');
          if (this.isListening) {
            this.pollTimer = setTimeout(poll, pollInterval * 3);
          }
          return;
        }
      }

      // 修复：使用非递归方式避免内存泄漏
      if (this.isListening) {
        this.pollTimer = setTimeout(poll, pollInterval);
      } else {
        console.log('⏹️  轮询在处理过程中被停止');
      }
    };

    // 立即执行一次，然后开始定时轮询
    poll();
  }

  /**
   * 检查网络连接
   */
  private async checkNetworkConnection(): Promise<boolean> {
    try {
      await this.provider.getBlockNumber();
      return true;
    } catch (error) {
      console.error('🌐 网络连接检查失败:', error);
      return false;
    }
  }

  /**
   * 修复：安全地回滚事务
   */
  private async safeRollback(transaction: any, context: string): Promise<void> {
    if (!transaction) {
      return;
    }

    try {
      await transaction.rollback();
    } catch (rollbackError: any) {
      // 忽略已经完成的事务错误
      if (!rollbackError.message.includes('finished') &&
          !rollbackError.message.includes('rollback') &&
          !rollbackError.message.includes('already been committed or rolled back')) {
        console.warn(`${context} 事务回滚失败:`, rollbackError);
      }
    }
  }

  /**
   * 带重试的网络操作
   */
  private async withRetry<T>(operation: () => Promise<T>, maxRetries: number = 3): Promise<T> {
    let lastError: Error;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error as Error;
        console.warn(`🔄 操作失败，第 ${attempt}/${maxRetries} 次重试: ${lastError.message}`);

        if (attempt < maxRetries) {
          // 指数退避：1秒、2秒、4秒
          const delay = Math.pow(2, attempt - 1) * 1000;
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    throw lastError!;
  }

  /**
   * 修复：改进事件处理，添加细粒度跟踪
   */
  private async checkForNewDeposits(): Promise<void> {
    try {
      // 使用重试机制获取当前区块
      const currentBlock = await this.withRetry(() => this.provider.getBlockNumber());
      console.log(`📊 当前区块: ${currentBlock}, 最后处理区块: ${this.lastProcessedBlock}`);

      if (currentBlock <= this.lastProcessedBlock) {
        console.log('⏭️  没有新区块需要处理');
        return;
      }

      const fromBlock = this.lastProcessedBlock + 1;

      // 修复：动态调整区块范围，避免RPC限制
      const maxBlockRange = Math.min(1000, currentBlock - fromBlock + 1); // 减少到1000个区块更安全
      const toBlock = Math.min(currentBlock, fromBlock + maxBlockRange - 1);

      console.log(`🔍 查询区块范围: ${fromBlock} 到 ${toBlock} (当前最新: ${currentBlock})`);

      // 验证区块范围的有效性
      if (fromBlock > currentBlock) {
        console.log('⚠️  起始区块超过当前区块，跳过查询');
        return;
      }

      if (fromBlock > toBlock) {
        console.log('⚠️  起始区块大于结束区块，跳过查询');
        return;
      }

      // 查询从上次处理的区块到指定区块的事件
      const filter = this.contract.filters.Deposit();
      console.log(`🔧 使用过滤器查询事件...`);

      const events = await this.contract.queryFilter(
        filter,
        fromBlock,
        toBlock
      );

      console.log(`📡 查询结果: 发现 ${events.length} 个充值事件`);

      if (events.length > 0) {
        console.log(`✅ 检查区块 ${fromBlock} 到 ${toBlock}，发现 ${events.length} 个充值事件`);

        // 显示事件详情
        events.forEach((event, index) => {
          if ('args' in event && event.args) {
            console.log(`   事件 ${index + 1}: 区块 ${event.blockNumber}, 交易 ${event.transactionHash}`);
            console.log(`      用户: ${event.args[0]}, 金额: ${ethers.formatEther(event.args[1])} PHRS`);
          }
        });
      } else {
        console.log(`ℹ️  区块 ${fromBlock} 到 ${toBlock} 中没有充值事件`);
      }

      // 修复：改进事件处理，记录每个事件的处理状态
      const eventResults = await this.processEventsWithTracking(events);
      
      // 修复：只有所有事件都成功处理时才更新区块号
      if (eventResults.allSuccessful) {
        this.lastProcessedBlock = toBlock;
        await this.saveLastProcessedBlock();
        console.log(`📝 更新最后处理区块号: ${toBlock}`);
      } else {
        console.warn(`⚠️  有 ${eventResults.failureCount} 个事件处理失败，不更新区块号`);
        console.warn(`   成功处理: ${eventResults.successCount} 个事件`);
        console.warn(`   失败事件将在下次轮询时重试`);
      }

    } catch (error) {
      console.error('检查新充值事件时出错:', error);
      console.error('错误详情:', error);
    }
  }

  /**
   * 修复：新增事件批量处理方法，提供更好的错误跟踪
   */
  private async processEventsWithTracking(events: any[]): Promise<{
    successCount: number;
    failureCount: number;
    allSuccessful: boolean;
    processedEvents: string[];
    failedEvents: { txHash: string; error: string }[];
  }> {
    let successCount = 0;
    let failureCount = 0;
    const processedEvents: string[] = [];
    const failedEvents: { txHash: string; error: string }[] = [];

    for (const event of events) {
      try {
        if ('args' in event && event.args && event.args.length >= 3) {
          const eventLog = event as ethers.EventLog;
          console.log(`🔄 处理事件: 区块 ${eventLog.blockNumber}, 交易 ${eventLog.transactionHash}`);

          // 修复：在处理前先检查是否已存在记录，避免不必要的处理
          const existingRecord = await PhrsDeposit.findOne({
            where: { transactionHash: eventLog.transactionHash }
          });

          if (existingRecord) {
            console.log(`⚠️  事件已处理过，跳过: ${eventLog.transactionHash}`);
            successCount++; // 算作成功，因为记录已存在
            processedEvents.push(eventLog.transactionHash);
            continue;
          }

          await this.handleDepositEvent(
            eventLog.args[0] as string,
            eventLog.args[1] as bigint,
            eventLog.args[2] as bigint,
            BigInt(0),
            eventLog
          );

          console.log(`✅ 事件处理完成: 区块 ${eventLog.blockNumber}`);
          successCount++;
          processedEvents.push(eventLog.transactionHash);
        }
      } catch (error) {
        console.error('处理单个充值事件时出错:', error);

        // 修复：如果是唯一约束冲突，不算作失败
        if (error instanceof Error &&
            (error.name === 'SequelizeUniqueConstraintError' ||
             error.message.includes('Duplicate entry') ||
             error.message.includes('UNIQUE constraint failed'))) {
          console.log(`⚠️  事件重复处理（并发冲突），算作成功: ${event.transactionHash}`);
          successCount++;
          processedEvents.push(event.transactionHash);
        } else {
          failureCount++;
          failedEvents.push({
            txHash: event.transactionHash,
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      }
    }

    return {
      successCount,
      failureCount,
      allSuccessful: failureCount === 0,
      processedEvents,
      failedEvents
    };
  }

  /**
   * 修复：安全地记录失败事件
   */
  private async recordFailedEvent(
    user: string,
    amount: bigint,
    timestamp: bigint,
    event: ethers.EventLog,
    error: any
  ): Promise<void> {
    // 修复：先检查是否已存在记录（无事务检查）
    const existingRecord = await PhrsDeposit.findOne({
      where: { transactionHash: event.transactionHash }
    });

    if (existingRecord) {
      console.log(`⚠️  失败充值记录已存在，跳过: ${event.transactionHash}`);
      return;
    }

    let failureTransaction;
    let transactionCommitted = false;

    try {
      failureTransaction = await sequelize.transaction();

      // 在事务中再次检查（防止并发）
      const existingRecordWithLock = await PhrsDeposit.findOne({
        where: { transactionHash: event.transactionHash },
        lock: failureTransaction.LOCK.UPDATE,
        transaction: failureTransaction
      });

      if (existingRecordWithLock) {
        await this.safeRollback(failureTransaction, '失败记录检查');
        transactionCommitted = true;
        console.log(`⚠️  失败充值记录已存在（并发检查），跳过: ${event.transactionHash}`);
        return;
      }

      await PhrsDeposit.create({
        walletId: null,
        userAddress: user.toLowerCase(),
        amount: ethers.formatEther(amount),
        transactionHash: event.transactionHash,
        blockNumber: event.blockNumber,
        blockTimestamp: new Date(Number(timestamp) * 1000),
        contractAddress: this.contractAddress,
        status: 'FAILED',
        confirmations: 0,
        processedAt: new Date(),
        errorMessage: error instanceof Error ? error.message : 'Unknown error'
      }, { transaction: failureTransaction });

      await failureTransaction.commit();
      transactionCommitted = true;
      console.log(`📝 已记录失败充值事件: ${event.transactionHash}`);

    } catch (recordError: any) {
      // 修复：使用安全回滚方法
      if (failureTransaction && !transactionCommitted) {
        await this.safeRollback(failureTransaction, '失败记录创建');
        transactionCommitted = true;
      }

      if (recordError.name === 'SequelizeUniqueConstraintError') {
        console.log(`⚠️  失败充值记录已存在（并发创建），跳过: ${event.transactionHash}`);
      } else {
        console.error('记录失败充值事件时出错:', recordError);
      }
    }
  }

  /**
   * 修复：改进未注册用户处理，使用简化的事务逻辑
   */
  private async handleUnregisteredUserDeposit(
    user: string,
    amount: bigint,
    timestamp: bigint,
    event: ethers.EventLog
  ): Promise<void> {
    // 修复：先检查是否已存在记录（无事务检查）
    const existingRecord = await PhrsDeposit.findOne({
      where: { transactionHash: event.transactionHash }
    });

    if (existingRecord) {
      console.log(`⚠️  未注册用户充值记录已存在，跳过: ${event.transactionHash}`);
      return;
    }

    // 修复：使用更安全的事务处理
    let transaction;
    let transactionCommitted = false;

    try {
      transaction = await sequelize.transaction();

      // 修复：在事务中再次检查（防止并发）
      const existingRecordWithLock = await PhrsDeposit.findOne({
        where: { transactionHash: event.transactionHash },
        lock: transaction.LOCK.UPDATE,
        transaction
      });

      if (existingRecordWithLock) {
        console.log(`⚠️  未注册用户充值记录已存在（并发检查），跳过: ${event.transactionHash}`);
        await this.safeRollback(transaction, '未注册用户检查');
        transactionCommitted = true;
        return;
      }

      // 修复：创建记录，状态设为FAILED表示用户未注册
      await PhrsDeposit.create({
        walletId: null,
        userAddress: user.toLowerCase(),
        amount: ethers.formatEther(amount),
        transactionHash: event.transactionHash,
        blockNumber: event.blockNumber,
        blockTimestamp: new Date(Number(timestamp) * 1000),
        contractAddress: this.contractAddress,
        status: 'FAILED', // 修复：确保状态值在枚举范围内
        confirmations: 1,
        processedAt: new Date(),
        errorMessage: 'User wallet not found'
      }, { transaction });

      await transaction.commit();
      transactionCommitted = true;
      console.log(`✅ 已记录未注册用户的充值失败: ${event.transactionHash}`);

    } catch (error: any) {
      // 修复：使用安全回滚方法
      if (transaction && !transactionCommitted) {
        await this.safeRollback(transaction, '未注册用户记录创建');
        transactionCommitted = true;
      }

      if (error.name === 'SequelizeUniqueConstraintError') {
        console.log(`⚠️  未注册用户充值记录已存在（并发创建），跳过: ${event.transactionHash}`);
        // 修复：不重新抛出错误，避免上层事务问题
      } else {
        console.error('未注册用户记录创建失败:', error);
        // 修复：不重新抛出错误，避免上层事务问题
      }
    }
  }

  /**
   * 修复：改进事务处理逻辑
   */
  private async handleDepositEvent(
    user: string,
    amount: bigint,
    timestamp: bigint,
    depositId: bigint = BigInt(0),
    event: ethers.EventLog
  ): Promise<void> {
    console.log(`收到PHRS充值事件:`, {
      user,
      amount: ethers.formatEther(amount),
      timestamp: Number(timestamp),
      depositId: Number(depositId),
      txHash: event.transactionHash,
      blockNumber: event.blockNumber
    });

    // 修复：首先检查是否已处理过（无事务）
    const existingDeposit = await PhrsDeposit.findOne({
      where: { transactionHash: event.transactionHash }
    });

    if (existingDeposit) {
      console.log(`交易 ${event.transactionHash} 已处理过，跳过`);
      return;
    }

    // 修复：查找用户钱包（无事务）
    const userWallet = await UserWallet.findOne({
      where: { phrsWalletAddress: user.toLowerCase() }
    });

    if (!userWallet) {
      console.log(`未找到地址 ${user} 对应的用户钱包，创建充值记录但不更新余额`);
      // 修复：直接处理未注册用户，避免嵌套事务
      await this.handleUnregisteredUserDeposit(user, amount, timestamp, event);
      return;
    }

    // 修复：只有找到用户钱包时才使用事务
    let transaction;
    let transactionCommitted = false;

    try {
      transaction = await sequelize.transaction();

      // 修复：在事务中再次检查重复（防止并发）
      const existingDepositWithLock = await PhrsDeposit.findOne({
        where: { transactionHash: event.transactionHash },
        lock: transaction.LOCK.UPDATE,
        transaction
      });

      if (existingDepositWithLock) {
        console.log(`交易 ${event.transactionHash} 已处理过（并发检查），跳过`);
        await this.safeRollback(transaction, '主事务重复检查');
        transactionCommitted = true;
        return;
      }

      // 修复：使用更高精度的BigNumber计算
      const depositAmount = new BigNumber(ethers.formatEther(amount));
      const currentBalance = new BigNumber(userWallet.phrsBalance?.toString() || '0');
      const newBalance = currentBalance.plus(depositAmount);

      // 修复：使用更高精度存储余额
      await userWallet.update({
        phrsBalance: newBalance.toFixed(18), // 修复：使用18位精度
        phrsWalletAddress: user.toLowerCase(),
        lastPhrsUpdateTime: new Date()
      }, { transaction });

      // 创建充值记录
      await PhrsDeposit.create({
        walletId: userWallet.id,
        userAddress: user.toLowerCase(),
        amount: depositAmount.toFixed(18), // 修复：使用18位精度
        transactionHash: event.transactionHash,
        blockNumber: event.blockNumber,
        blockTimestamp: new Date(Number(timestamp) * 1000),
        contractAddress: this.contractAddress,
        status: 'CONFIRMED',
        confirmations: 1,
        processedAt: new Date()
      }, { transaction });

      await transaction.commit();
      transactionCommitted = true; // 修复：标记事务已完成

      console.log(`✅ PHRS充值处理成功:`, {
        walletId: userWallet.id,
        user,
        amount: depositAmount.toFixed(18),
        newBalance: newBalance.toFixed(18),
        txHash: event.transactionHash
      });

    } catch (error) {
      // 修复：使用安全回滚方法
      if (transaction && !transactionCommitted) {
        await this.safeRollback(transaction, '主事务处理');
        transactionCommitted = true;
      }

      console.error('❌ 处理PHRS充值事件失败:', error);

      // 修复：记录失败事件到数据库（使用独立事务）
      await this.recordFailedEvent(user, amount, timestamp, event, error);

      // 修复：重新抛出错误让上层处理
      throw error;
    }
  }

  /**
   * 处理历史事件 - 分批处理所有未处理的历史交易
   */
  private async processHistoricalEvents(): Promise<void> {
    try {
      const currentBlock = await this.provider.getBlockNumber();

      // 确定起始区块：取最后处理区块+1 和 配置的历史起始区块 中的较大值
      let fromBlock = Math.max(this.lastProcessedBlock + 1, this.historicalStartBlock);

      if (fromBlock >= currentBlock) {
        console.log('没有需要处理的历史事件');
        return;
      }

      console.log(`🔍 开始处理历史PHRS充值事件，从区块 ${fromBlock} 到 ${currentBlock}`);
      console.log(`   配置的历史起始区块: ${this.historicalStartBlock}`);
      console.log(`   最后处理区块: ${this.lastProcessedBlock}`);

      const maxBlockRange = 1000; // 修复：减少批处理大小
      let totalProcessedEvents = 0;
      let batchCount = 0;

      // 分批处理历史事件
      while (fromBlock < currentBlock) {
        batchCount++;
        const toBlock = Math.min(fromBlock + maxBlockRange - 1, currentBlock);

        console.log(`📦 处理批次 ${batchCount}: 区块 ${fromBlock} 到 ${toBlock}`);

        try {
          // 使用重试机制查询事件
          const filter = this.contract.filters.Deposit();
          const events = await this.withRetry(() =>
            this.contract.queryFilter(filter, fromBlock, toBlock)
          );

          console.log(`   找到 ${events.length} 个充值事件`);

          if (events.length > 0) {
            // 修复：使用新的事件处理方法
            const eventResults = await this.processEventsWithTracking(events);
            
            // 只有在所有事件都成功处理时才更新进度
            if (eventResults.allSuccessful) {
              this.lastProcessedBlock = toBlock;
              await this.saveLastProcessedBlock();
              console.log(`   ✅ 批次 ${batchCount} 处理完成，已处理到区块 ${toBlock}`);
              totalProcessedEvents += eventResults.successCount;
            } else {
              console.warn(`   ⚠️  批次 ${batchCount} 部分失败: 成功 ${eventResults.successCount}, 失败 ${eventResults.failureCount}`);
              console.warn(`   不更新进度，失败的事件将在下次处理时重试`);
              break; // 停止处理后续批次
            }
          } else {
            // 没有事件也可以更新进度
            this.lastProcessedBlock = toBlock;
            await this.saveLastProcessedBlock();
          }

          // 添加短暂延迟避免过载
          if (events.length > 10) {
            console.log('   ⏳ 短暂延迟以避免系统过载...');
            await new Promise(resolve => setTimeout(resolve, 1000));
          }

        } catch (error) {
          console.error(`   ❌ 批次 ${batchCount} 查询失败:`, error);
          console.warn(`   停止历史事件处理，等待下次重试`);
          break;
        }

        fromBlock = toBlock + 1;
      }

      console.log(`🎉 历史事件处理完成！`);
      console.log(`   总批次数: ${batchCount}`);
      console.log(`   总处理事件数: ${totalProcessedEvents}`);
      console.log(`   最终处理到区块: ${this.lastProcessedBlock}`);

    } catch (error) {
      console.error('❌ 处理历史事件失败:', error);
    }
  }

  /**
   * 加载最后处理的区块号
   */
  private async loadLastProcessedBlock(): Promise<void> {
    try {
      // 从数据库获取最后处理的区块号
      const lastDeposit = await PhrsDeposit.findOne({
        order: [['blockNumber', 'DESC']],
        limit: 1
      });
      
      if (lastDeposit) {
        this.lastProcessedBlock = Number(lastDeposit.blockNumber);
        console.log(`从数据库加载最后处理的区块号: ${this.lastProcessedBlock}`);
      } else {
        // 如果没有记录，使用配置的历史起始区块
        if (this.historicalStartBlock > 0) {
          this.lastProcessedBlock = this.historicalStartBlock - 1;
          console.log(`没有历史记录，使用配置的起始区块: ${this.historicalStartBlock}`);
        } else {
          // 如果没有配置历史起始区块，从当前区块开始
          this.lastProcessedBlock = await this.provider.getBlockNumber();
          console.log(`没有历史记录且未配置起始区块，从当前区块开始: ${this.lastProcessedBlock}`);
        }
      }

      console.log(`最后处理的区块号设置为: ${this.lastProcessedBlock}`);
    } catch (error) {
      console.error('加载最后处理区块号失败:', error);
      this.lastProcessedBlock = await this.provider.getBlockNumber();
    }
  }

  /**
   * 保存最后处理的区块号
   */
  private async saveLastProcessedBlock(): Promise<void> {
    console.log(`已处理到区块: ${this.lastProcessedBlock}`);
  }

  /**
   * 手动同步指定用户的PHRS余额
   */
  public async syncUserBalance(walletAddress: string): Promise<void> {
    try {
      console.log(`开始同步用户 ${walletAddress} 的PHRS余额`);
      
      // 查找用户钱包
      const userWallet = await UserWallet.findOne({
        where: { phrsWalletAddress: walletAddress.toLowerCase() }
      });

      if (!userWallet) {
        throw new Error(`未找到地址 ${walletAddress} 对应的用户钱包`);
      }

      // 获取用户的所有确认充值记录
      const deposits = await PhrsDeposit.findAll({
        where: {
          walletId: userWallet.id,
          status: 'CONFIRMED'
        }
      });

      // 计算总余额
      let totalBalance = new BigNumber(0);
      for (const deposit of deposits) {
        totalBalance = totalBalance.plus(new BigNumber(deposit.amount.toString()));
      }

      // 更新用户余额
      await userWallet.update({
        phrsBalance: totalBalance.toFixed(18), // 修复：使用18位精度
        lastPhrsUpdateTime: new Date()
      });

      console.log(`✅ 用户 ${walletAddress} PHRS余额同步完成: ${totalBalance.toFixed(18)}`);
      
    } catch (error) {
      console.error(`❌ 同步用户 ${walletAddress} PHRS余额失败:`, error);
      throw error;
    }
  }

  /**
   * 获取服务状态
   */
  public getStatus(): {
    isListening: boolean;
    contractAddress: string;
    lastProcessedBlock: number;
    providerUrl: string;
  } {
    return {
      isListening: this.isListening,
      contractAddress: this.contractAddress,
      lastProcessedBlock: this.lastProcessedBlock,
      providerUrl: this.provider._getConnection().url
    };
  }

  /**
   * 健康检查
   */
  public async healthCheck(): Promise<{
    status: 'healthy' | 'unhealthy';
    details: {
      networkConnection: boolean;
      databaseConnection: boolean;
      serviceRunning: boolean;
      lastProcessedBlock: number;
      currentBlock?: number;
      blockLag?: number;
    };
  }> {
    const details = {
      networkConnection: false,
      databaseConnection: false,
      serviceRunning: this.isListening,
      lastProcessedBlock: this.lastProcessedBlock,
      currentBlock: undefined as number | undefined,
      blockLag: undefined as number | undefined
    };

    try {
      // 检查网络连接
      const currentBlock = await this.provider.getBlockNumber();
      details.networkConnection = true;
      details.currentBlock = currentBlock;
      details.blockLag = currentBlock - this.lastProcessedBlock;
    } catch (error) {
      console.error('健康检查 - 网络连接失败:', error);
    }

    try {
      // 检查数据库连接
      await sequelize.authenticate();
      details.databaseConnection = true;
    } catch (error) {
      console.error('健康检查 - 数据库连接失败:', error);
    }

    const isHealthy = details.networkConnection &&
                     details.databaseConnection &&
                     details.serviceRunning &&
                     (details.blockLag === undefined || details.blockLag < 100);

    return {
      status: isHealthy ? 'healthy' : 'unhealthy',
      details
    };
  }

  /**
   * 测试方法：手动处理指定区块范围的事件
   */
  public async testProcessBlocks(fromBlock: number, toBlock?: number): Promise<void> {
    let endBlock = toBlock || fromBlock;

    console.log(`🧪 开始测试处理区块 ${fromBlock} 到 ${endBlock}`);
    console.log('================================================');

    try {
      // 1. 检查网络连接
      const currentBlock = await this.provider.getBlockNumber();
      console.log(`📡 当前网络区块: ${currentBlock}`);

      if (fromBlock > currentBlock) {
        console.log('❌ 起始区块号超过当前区块');
        return;
      }

      // 确保结束区块不超过当前区块
      if (endBlock > currentBlock) {
        console.log(`⚠️  结束区块号超过当前区块，调整为 ${currentBlock}`);
        endBlock = currentBlock;
      }

      // 修复：减少最大区块范围
      const maxBlockRange = 1000;
      if (endBlock - fromBlock + 1 > maxBlockRange) {
        const newEndBlock = fromBlock + maxBlockRange - 1;
        console.log(`⚠️  区块范围过大，调整结束区块为 ${newEndBlock} (最大范围: ${maxBlockRange}个区块)`);
        endBlock = newEndBlock;
      }

      if (fromBlock > endBlock) {
        console.log(`❌ 起始区块 ${fromBlock} 大于结束区块 ${endBlock}，无法查询`);
        return;
      }

      // 2. 查询指定区块范围的事件
      console.log(`\n🔍 查询区块 ${fromBlock} 到 ${endBlock} 的事件...`);
      const filter = this.contract.filters.Deposit();
      const events = await this.contract.queryFilter(filter, fromBlock, endBlock);

      console.log(`📡 找到 ${events.length} 个Deposit事件`);

      if (events.length === 0) {
        console.log('ℹ️  指定区块范围内没有充值事件');
        return;
      }

      // 3. 显示事件详情
      console.log(`\n📝 事件详情:`);
      events.forEach((event, index) => {
        if ('args' in event && event.args) {
          console.log(`   事件 ${index + 1}:`);
          console.log(`     区块: ${event.blockNumber}`);
          console.log(`     交易: ${event.transactionHash}`);
          console.log(`     用户: ${event.args[0]}`);
          console.log(`     金额: ${ethers.formatEther(event.args[1])} PHRS`);
          console.log(`     时间: ${new Date(Number(event.args[2]) * 1000).toLocaleString()}`);
        }
      });

      // 4. 处理事件
      console.log(`\n🔄 开始处理事件...`);
      const eventResults = await this.processEventsWithTracking(events);

      // 5. 显示处理结果
      console.log(`\n📊 处理结果统计:`);
      console.log(`================================================`);
      console.log(`📡 总事件数: ${events.length}`);
      console.log(`✅ 成功处理: ${eventResults.successCount}`);
      console.log(`❌ 处理失败: ${eventResults.failureCount}`);

      if (eventResults.successCount > 0) {
        console.log(`\n🎉 测试完成！成功处理了 ${eventResults.successCount} 个事件`);
      } else {
        console.log(`\n⚠️  没有成功处理任何事件，请检查日志`);
      }

      // 显示失败详情
      if (eventResults.failedEvents.length > 0) {
        console.log(`\n❌ 失败事件详情:`);
        eventResults.failedEvents.forEach((failed, index) => {
          console.log(`   ${index + 1}. 交易: ${failed.txHash}`);
          console.log(`      错误: ${failed.error}`);
        });
      }

    } catch (error) {
      console.error('❌ 测试过程中发生错误:', error);
      throw error;
    }
  }

  /**
   * 测试方法：重置最后处理的区块号
   */
  public async testResetLastProcessedBlock(blockNumber: number): Promise<void> {
    console.log(`🔄 重置最后处理区块号: ${this.lastProcessedBlock} → ${blockNumber}`);
    this.lastProcessedBlock = blockNumber;
    await this.saveLastProcessedBlock();
    console.log(`✅ 重置完成`);
  }
}

// 导出单例实例
export const phrsDepositService = new PhrsDepositService();
