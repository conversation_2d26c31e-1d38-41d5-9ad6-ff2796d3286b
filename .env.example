# Wolf Fun - PHRS代币支付系统环境变量配置模板
# 复制此文件为 .env 并填入实际值

# ===========================================
# 基本应用配置
# ===========================================

# 运行环境 (development, production, test)
NODE_ENV=production

# 服务器端口
PORT=3456

# 应用基础URL
BASE_URL=https://your-domain.com

# ===========================================
# 数据库配置
# ===========================================

# PostgreSQL数据库连接URL
DATABASE_URL=postgresql://username:password@localhost:5432/wolf_fun

# 数据库连接池配置（可选）
DB_POOL_MIN=2
DB_POOL_MAX=10

# ===========================================
# JWT认证配置
# ===========================================

# JWT密钥（请使用强随机字符串）
JWT_SECRET=your-super-secure-jwt-secret-here-change-this-in-production

# JWT过期时间（默认24小时）
JWT_EXPIRES_IN=24h

# ===========================================
# Pharos网络配置
# ===========================================

# Pharos主网RPC URL
PHAROS_RPC_URL=https://rpc.pharos.network

# Pharos测试网RPC URL（用于开发和测试）
PHAROS_TESTNET_RPC_URL=https://testnet-rpc.pharos.network

# 当前使用的网络（mainnet 或 testnet）
PHAROS_NETWORK=mainnet

# ===========================================
# PHRS代币系统配置
# ===========================================

# PHRS代币合约地址
PHRS_TOKEN_ADDRESS=0x1234567890123456789012345678901234567890

# PHRS充值合约地址
PHRS_DEPOSIT_CONTRACT_ADDRESS=0xabcdefabcdefabcdefabcdefabcdefabcdefabcd

# 充值限制配置
MIN_DEPOSIT_AMOUNT=1
MAX_DEPOSIT_AMOUNT=10000

# ===========================================
# 区块链浏览器配置（用于合约验证）
# ===========================================

# Pharos主网浏览器API
PHAROS_MAINNET_EXPLORER=https://explorer.pharos.network
PHAROS_MAINNET_EXPLORER_API=https://explorer.pharos.network/api

# Pharos测试网浏览器API
PHAROS_TESTNET_EXPLORER=https://testnet-explorer.pharos.network
PHAROS_TESTNET_EXPLORER_API=https://testnet-explorer.pharos.network/api

# 浏览器API密钥（用于合约验证）
PHAROS_EXPLORER_API_KEY=your-explorer-api-key-here

# ===========================================
# Redis配置（可选，用于缓存和会话存储）
# ===========================================

# Redis连接URL
REDIS_URL=redis://localhost:6379

# Redis密码（如果需要）
REDIS_PASSWORD=

# Redis数据库编号
REDIS_DB=0

# ===========================================
# 日志配置
# ===========================================

# 日志级别 (error, warn, info, debug)
LOG_LEVEL=info

# 日志文件路径
LOG_FILE_PATH=./logs

# 是否启用控制台日志
LOG_CONSOLE=true

# ===========================================
# 邮件配置（用于通知）
# ===========================================

# SMTP服务器配置
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# 发件人信息
EMAIL_FROM=Wolf Fun <<EMAIL>>

# 管理员邮箱（接收系统通知）
ADMIN_EMAIL=<EMAIL>

# ===========================================
# 监控和告警配置
# ===========================================

# 是否启用性能监控
ENABLE_MONITORING=true

# 健康检查间隔（秒）
HEALTH_CHECK_INTERVAL=300

# 错误率告警阈值（百分比）
ERROR_RATE_THRESHOLD=5

# 响应时间告警阈值（毫秒）
RESPONSE_TIME_THRESHOLD=5000

# ===========================================
# 安全配置
# ===========================================

# CORS允许的域名（多个域名用逗号分隔）
CORS_ORIGINS=https://your-frontend-domain.com,https://admin.your-domain.com

# 请求频率限制（每分钟最大请求数）
RATE_LIMIT_MAX=100

# 请求频率限制窗口时间（毫秒）
RATE_LIMIT_WINDOW=60000

# 是否启用HTTPS重定向
FORCE_HTTPS=true

# ===========================================
# 第三方服务配置
# ===========================================

# Telegram Bot配置（如果使用）
TELEGRAM_BOT_TOKEN=your-telegram-bot-token
TELEGRAM_WEBHOOK_URL=https://your-domain.com/api/telegram/webhook

# 推送通知服务配置
PUSH_SERVICE_KEY=your-push-service-key

# ===========================================
# 开发和测试配置
# ===========================================

# 是否启用调试模式
DEBUG=false

# 测试数据库URL（仅用于测试）
TEST_DATABASE_URL=postgresql://username:password@localhost:5432/wolf_fun_test

# 是否启用API文档
ENABLE_API_DOCS=false

# API文档路径
API_DOCS_PATH=/api-docs

# ===========================================
# 性能优化配置
# ===========================================

# 数据库查询超时时间（毫秒）
DB_QUERY_TIMEOUT=30000

# HTTP请求超时时间（毫秒）
HTTP_TIMEOUT=10000

# 缓存过期时间（秒）
CACHE_TTL=3600

# 是否启用压缩
ENABLE_COMPRESSION=true

# ===========================================
# 备份配置
# ===========================================

# 备份存储路径
BACKUP_PATH=/backups/wolf_fun

# 备份保留天数
BACKUP_RETENTION_DAYS=30

# 是否启用自动备份
AUTO_BACKUP_ENABLED=true

# 备份时间（cron格式）
BACKUP_SCHEDULE=0 2 * * *

# ===========================================
# 特殊功能配置
# ===========================================

# 是否启用维护模式
MAINTENANCE_MODE=false

# 维护模式消息
MAINTENANCE_MESSAGE=System is under maintenance. Please try again later.

# 是否启用测试API（仅开发环境）
ENABLE_TEST_APIS=false

# 管理员API密钥
ADMIN_API_KEY=your-admin-api-key-here

# ===========================================
# 部署相关配置
# ===========================================

# 部署环境标识
DEPLOYMENT_ENV=production

# 版本号
APP_VERSION=1.0.0

# 构建时间戳
BUILD_TIMESTAMP=

# Git提交哈希
GIT_COMMIT_HASH=

# ===========================================
# 注意事项
# ===========================================

# 1. 请确保所有密钥和密码都使用强随机字符串
# 2. 生产环境中请删除或注释掉测试相关配置
# 3. 定期轮换JWT密钥和其他敏感信息
# 4. 确保.env文件不被提交到版本控制系统
# 5. 使用环境变量管理工具（如Docker secrets）来管理敏感信息
